# 安全头配置

# HSTS - 强制HTTPS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

# 防止点击劫持
add_header X-Frame-Options "SAMEORIGIN" always;

# 防止MIME类型嗅探
add_header X-Content-Type-Options "nosniff" always;

# XSS保护
add_header X-XSS-Protection "1; mode=block" always;

# 引用策略
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# 权限策略（根据需要调整）
add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;

# CSP内容安全策略（根据应用需求调整）
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" always;

# 移除敏感信息（通过proxy_hide_header在proxy-common.conf中处理）
# 注意：标准nginx无more_clear_headers指令，敏感信息隐藏在proxy-common.conf中处理
